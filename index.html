<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能背单词 - Llama 3.2 AI视觉识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <!-- Tesseract.js OCR库 -->
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
        .progress-bar { transition: width 0.5s ease-in-out; }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        /* 打字模式样式 */
        .typing-input {
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }
        .typing-input:focus {
            transform: scale(1.1);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }
        .typing-input:disabled {
            cursor: not-allowed;
        }
        .typing-input.bg-green-100 {
            animation: correctPulse 0.6s ease-in-out;
        }
        .typing-input.bg-red-100 {
            animation: incorrectShake 0.6s ease-in-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1); }
        }

        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fas fa-book-open mr-3"></i>
                    智能背单词
                </h1>
                <div class="flex items-center space-x-4">
                    <button id="statsBtn" class="hover:bg-white hover:bg-opacity-20 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>统计
                    </button>
                    <button id="settingsBtn" class="hover:bg-white hover:bg-opacity-20 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-cog mr-2"></i>设置
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- 词表管理区域 -->
        <div id="wordlistManager" class="bg-white rounded-xl card-shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-list-alt mr-3 text-blue-600"></i>
                词表管理
            </h2>

            <!-- 词表操作栏 -->
            <div class="flex flex-wrap items-center gap-3 mb-4">
                <input type="text" id="wordlistName" placeholder="词表名称"
                       class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <select id="wordlistSelect" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">选择词表</option>
                </select>
                <button id="saveWordlistBtn" class="btn-primary text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
                <button id="newWordlistBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>新建
                </button>
                <button id="deleteWordlistBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-trash mr-2"></i>删除
                </button>
            </div>

            <!-- 导入导出 -->
            <div class="flex flex-wrap items-center gap-3 mb-4">
                <button id="exportBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-download mr-2"></i>导出
                </button>
                <button id="importBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-upload mr-2"></i>导入
                </button>
                <button id="ocrBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-brain mr-2"></i>AI视觉识别
                </button>
                <input type="file" id="importFile" accept=".json" class="hidden">
                <input type="file" id="ocrImageFile" accept="image/*" class="hidden">
            </div>

            <!-- 单词输入区域 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    单词列表 (格式: 中文 英文 或 中文,英文)
                </label>
                <textarea id="wordlistContent" rows="8"
                          placeholder="例如：&#10;苹果 apple&#10;香蕉 banana&#10;橙子 orange"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- OCR识别区域 -->
            <div id="ocrSection" class="ocr-section bg-purple-50 p-4 rounded-lg mb-4 hidden">
                <h3 class="text-lg font-semibold mb-3 flex items-center">
                    <i class="fas fa-brain mr-2 text-green-600"></i>
                    Llama 3.2 AI视觉识别
                </h3>
                <div class="mb-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-star mr-1"></i>
                        Meta开源 • 完全免费 • 手写体识别95%+
                    </span>
                </div>

                <!-- 图片预览区域 -->
                <div id="imagePreview" class="mb-4 hidden">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">上传的图片:</span>
                        <button id="clearImageBtn" class="clear-image-btn text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-times mr-1"></i>清除
                        </button>
                    </div>
                    <div class="image-preview-container border-2 border-dashed border-purple-300 rounded-lg p-4 text-center">
                        <img id="previewImage" class="max-w-full max-h-64 mx-auto rounded-lg shadow-md" alt="预览图片">
                    </div>
                </div>

                <!-- AI视觉识别引擎 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">AI视觉识别引擎:</label>
                    <div class="max-w-lg">
                        <label class="ocr-engine-card flex items-center p-4 border-2 border-green-300 bg-green-50 rounded-lg cursor-pointer hover:border-green-400 relative">
                            <input type="radio" name="ocrEngine" value="openrouter" checked class="mr-3 ocr-engine-radio">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 text-lg">OpenRouter - DeepSeek Chat</div>
                                <div class="text-sm text-gray-600 mt-1">DeepSeek强大的对话模型，支持智能词汇配对，完全免费使用</div>
                                <div class="text-xs text-green-600 mt-2 flex items-center gap-3">
                                    <span>✓ 完全免费</span>
                                    <span>✓ 智能配对</span>
                                    <span>✓ 中英文支持</span>
                                    <span>✓ 语义理解</span>
                                </div>
                            </div>
                            <span class="engine-badge bg-gradient-to-r from-green-500 to-emerald-500">免费</span>
                        </label>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        使用DeepSeek最新的对话模型，通过OpenRouter免费提供，智能配对准确率高达95%+
                    </div>
                </div>

                <!-- OpenRouter API配置 -->
                <div id="apiConfig" class="mb-4">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-key text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-800">OpenRouter API配置</span>
                        </div>

                        <div class="grid grid-cols-1 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">API Key:</label>
                                <input type="text" id="openrouterApiKey" placeholder="输入您的OpenRouter API Key 或 'demo' 体验演示" value="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"  class="api-input w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">选择模型:</label>
                                <select id="openrouterModel" class="api-input w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="deepseek/deepseek-chat:free" selected>🧠 DeepSeek Chat (免费推荐)</option>
                                    <option value="meta-llama/llama-3.2-11b-vision-instruct:free">🦙 Llama 3.2 11B Vision (免费)</option>
                                    <option value="google/gemini-flash-1.5">🔥 Gemini Flash 1.5 (免费)</option>
                                    <option value="google/gemini-pro-vision">💎 Gemini Pro Vision</option>
                                    <option value="anthropic/claude-3-haiku">🤖 Claude 3 Haiku</option>
                                    <option value="openai/gpt-4-vision-preview">🧠 GPT-4 Vision</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Base URL:</label>
                                <input type="text" id="openrouterBaseUrl" value="https://openrouter.ai/api/v1" class="api-input w-full px-3 py-2 border border-gray-300 rounded-lg text-sm bg-gray-50" readonly>
                            </div>
                        </div>

                        <div class="mt-3 text-xs text-green-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <i class="fas fa-info-circle mr-1"></i>
                                    没有API Key？
                                    <a href="https://openrouter.ai/" target="_blank" class="text-green-600 hover:underline font-medium">
                                        点击免费注册OpenRouter
                                    </a>
                                </div>
                                <div class="text-green-600 font-medium">
                                    ✓ DeepSeek完全免费使用
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 识别控制 -->
                <div class="flex items-center gap-3 mb-4">
                    <button id="startOcrBtn" class="ocr-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-brain mr-2"></i>开始AI识别
                    </button>
                    <div id="ocrProgress" class="ocr-progress flex items-center text-sm text-gray-600 hidden">
                        <div class="ocr-loading">
                            <div class="spinner"></div>
                            <span id="ocrProgressText">正在识别...</span>
                        </div>
                    </div>
                </div>

                <!-- 识别结果 -->
                <div id="ocrResults" class="ocr-results hidden">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-700">识别结果:</h4>
                        <div class="flex items-center gap-2">
                            <button id="autoMatchBtn" class="auto-match-btn text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded">
                                <i class="fas fa-magic mr-1"></i>智能配对
                            </button>
                            <button id="addOcrResultsBtn" class="add-results-btn text-sm bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded">
                                <i class="fas fa-plus mr-1"></i>添加到词表
                            </button>
                        </div>
                    </div>

                    <!-- 原始识别文本 -->
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">原始识别文本:</label>
                        <textarea id="rawOcrText" rows="3" class="ocr-text-area w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" readonly></textarea>
                    </div>

                    <!-- 词汇配对结果 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">词汇配对 (可编辑):</label>
                        <textarea id="pairedWords" rows="6" placeholder="系统将自动配对中英文词汇，您也可以手动编辑&#10;格式: 中文 英文" class="ocr-text-area w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"></textarea>
                        <div class="ocr-info text-xs text-gray-500 mt-1">
                            <i class="fas fa-info-circle mr-1"></i>
                            支持格式: "中文 英文" 或 "中文,英文"，每行一个词汇对
                        </div>
                    </div>
                </div>
            </div>

            <!-- 词表信息 -->
            <div id="wordlistInfo" class="bg-blue-50 p-4 rounded-lg hidden">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">当前词表:</span>
                    <span id="currentWordlistName" class="font-medium"></span>
                </div>
                <div class="flex items-center justify-between mt-2">
                    <span class="text-sm text-gray-600">单词数量:</span>
                    <span id="wordCount" class="font-medium text-blue-600"></span>
                </div>
            </div>
        </div>

        <!-- 学习模式选择 -->
        <div id="modeSelection" class="bg-white rounded-xl card-shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-graduation-cap mr-3 text-green-600"></i>
                学习模式
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <button id="spellingModeBtn" class="mode-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all transform hover:scale-105">
                    <i class="fas fa-keyboard text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">拼写模式</h3>
                    <p class="text-sm opacity-90">看中文写英文</p>
                </button>
                <button id="typingModeBtn" class="mode-card bg-gradient-to-br from-indigo-500 to-indigo-600 text-white p-6 rounded-xl hover:from-indigo-600 hover:to-indigo-700 transition-all transform hover:scale-105">
                    <i class="fas fa-edit text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">打字模式</h3>
                    <p class="text-sm opacity-90">逐字母输入</p>
                </button>
                <button id="choiceModeBtn" class="mode-card bg-gradient-to-br from-green-500 to-green-600 text-white p-6 rounded-xl hover:from-green-600 hover:to-green-700 transition-all transform hover:scale-105">
                    <i class="fas fa-list-ul text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">选择题模式</h3>
                    <p class="text-sm opacity-90">多选项选择</p>
                </button>
                <button id="reviewModeBtn" class="mode-card bg-gradient-to-br from-purple-500 to-purple-600 text-white p-6 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all transform hover:scale-105">
                    <i class="fas fa-redo text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">错题复习</h3>
                    <p class="text-sm opacity-90">复习错题</p>
                </button>
                <button id="spencerModeBtn" class="mode-card bg-gradient-to-br from-orange-500 to-red-600 text-white p-6 rounded-xl hover:from-orange-600 hover:to-red-700 transition-all transform hover:scale-105">
                    <i class="fas fa-brain text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">斯宾塞复习</h3>
                    <p class="text-sm opacity-90">间隔重复算法</p>
                </button>
            </div>
        </div>

        <!-- 学习界面 -->
        <div id="quizContainer" class="bg-white rounded-xl card-shadow p-6 hidden fade-in">
            <!-- 进度条 -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">学习进度</span>
                    <span id="progressText" class="text-sm text-gray-500">0/0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
            </div>

            <!-- 题目区域 -->
            <div class="text-center mb-8">
                <div id="questionText" class="text-2xl font-semibold text-gray-800 mb-4"></div>
                <div id="answerArea" class="mb-6"></div>
                <div id="resultArea" class="mb-4"></div>
            </div>

            <!-- 控制按钮 -->
            <div class="flex justify-center space-x-4">
                <button id="submitBtn" class="btn-primary text-white px-6 py-3 rounded-lg font-medium hidden">
                    <i class="fas fa-check mr-2"></i>提交答案
                </button>
                <button id="nextBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium hidden transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>下一题
                </button>
                <button id="backToMenuBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-home mr-2"></i>返回主页
                </button>
            </div>

            <!-- 统计信息 -->
            <div id="statsArea" class="mt-6 grid grid-cols-3 gap-4 text-center">
                <div class="bg-green-50 p-4 rounded-lg">
                    <div id="correctCount" class="text-2xl font-bold text-green-600">0</div>
                    <div class="text-sm text-gray-600">正确</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <div id="wrongCount" class="text-2xl font-bold text-red-600">0</div>
                    <div class="text-sm text-gray-600">错误</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div id="accuracyRate" class="text-2xl font-bold text-blue-600">0%</div>
                    <div class="text-sm text-gray-600">正确率</div>
                </div>
            </div>
        </div>

        <!-- 复习选择界面 -->
        <div id="reviewSelectionScreen" class="bg-white rounded-xl card-shadow p-6 hidden fade-in">
            <h2 class="text-2xl font-semibold mb-6 flex items-center">
                <i class="fas fa-filter mr-3 text-purple-600"></i>
                选择复习内容
            </h2>

            <!-- 快速选择预设 -->
            <div class="filter-section">
                <h3>快速选择</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button class="preset-btn bg-red-50 hover:bg-red-100 text-red-700 border border-red-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors" data-preset="difficult">
                        <i class="fas fa-exclamation-triangle mr-1"></i>困难单词
                    </button>
                    <button class="preset-btn bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border border-yellow-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors" data-preset="due">
                        <i class="fas fa-clock mr-1"></i>需要复习
                    </button>
                    <button class="preset-btn bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors" data-preset="beginner">
                        <i class="fas fa-seedling mr-1"></i>初学单词
                    </button>
                    <button class="preset-btn bg-green-50 hover:bg-green-100 text-green-700 border border-green-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors" data-preset="all">
                        <i class="fas fa-list mr-1"></i>全部错题
                    </button>
                </div>
            </div>

            <!-- 复习模式选择 -->
            <div class="filter-section">
                <h3>复习模式</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="review-mode-card flex items-center p-4 border-2 border-gray-200 rounded-lg">
                        <input type="radio" name="reviewMode" value="normal" checked class="mr-3 filter-checkbox">
                        <div>
                            <div class="font-medium">普通复习</div>
                            <div class="text-sm text-gray-600">传统错题复习模式</div>
                        </div>
                    </label>
                    <label class="review-mode-card flex items-center p-4 border-2 border-gray-200 rounded-lg">
                        <input type="radio" name="reviewMode" value="spencer" class="mr-3 filter-checkbox">
                        <div>
                            <div class="font-medium">斯宾塞复习</div>
                            <div class="text-sm text-gray-600">间隔重复算法</div>
                        </div>
                    </label>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="filter-section">
                <h3>筛选条件</h3>

                <!-- 掌握程度筛选 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">掌握程度</label>
                    <div class="filter-group">
                        <label>
                            <input type="checkbox" name="masteryLevel" value="0" checked class="filter-checkbox">
                            <span class="mastery-indicator mastery-0">级别0</span>
                        </label>
                        <label>
                            <input type="checkbox" name="masteryLevel" value="1" checked class="filter-checkbox">
                            <span class="mastery-indicator mastery-1">级别1</span>
                        </label>
                        <label>
                            <input type="checkbox" name="masteryLevel" value="2" checked class="filter-checkbox">
                            <span class="mastery-indicator mastery-2">级别2</span>
                        </label>
                        <label>
                            <input type="checkbox" name="masteryLevel" value="3" class="filter-checkbox">
                            <span class="mastery-indicator mastery-3">级别3</span>
                        </label>
                        <label>
                            <input type="checkbox" name="masteryLevel" value="4" class="filter-checkbox">
                            <span class="mastery-indicator mastery-4">级别4</span>
                        </label>
                        <label>
                            <input type="checkbox" name="masteryLevel" value="5" class="filter-checkbox">
                            <span class="mastery-indicator mastery-5">级别5</span>
                        </label>
                    </div>
                </div>

                <!-- 错误次数筛选 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">错误次数</label>
                    <div class="filter-group">
                        <label>
                            <input type="checkbox" name="wrongCount" value="1-2" checked class="filter-checkbox">
                            <span>1-2次</span>
                        </label>
                        <label>
                            <input type="checkbox" name="wrongCount" value="3-5" checked class="filter-checkbox">
                            <span>3-5次</span>
                        </label>
                        <label>
                            <input type="checkbox" name="wrongCount" value="6+" checked class="filter-checkbox">
                            <span>6次以上</span>
                        </label>
                    </div>
                </div>

                <!-- 复习状态筛选 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">复习状态</label>
                    <div class="filter-group">
                        <label>
                            <input type="checkbox" name="reviewStatus" value="due" checked class="filter-checkbox">
                            <span>需要复习</span>
                        </label>
                        <label>
                            <input type="checkbox" name="reviewStatus" value="learning" class="filter-checkbox">
                            <span>学习中</span>
                        </label>
                        <label>
                            <input type="checkbox" name="reviewStatus" value="all" class="filter-checkbox">
                            <span>全部</span>
                        </label>
                    </div>
                </div>

                <!-- 数量限制 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">复习数量</label>
                    <select id="reviewLimit" class="review-limit-select">
                        <option value="10">10个单词</option>
                        <option value="20" selected>20个单词</option>
                        <option value="30">30个单词</option>
                        <option value="50">50个单词</option>
                        <option value="all">全部</option>
                    </select>
                </div>
            </div>

            <!-- 预览统计 -->
            <div id="reviewPreview" class="review-preview mb-6 p-4 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-purple-700">
                        <i class="fas fa-filter mr-2"></i>符合条件的单词：
                    </span>
                    <span id="previewCount" class="text-2xl font-bold text-purple-600">0</span>
                </div>

                <!-- 单词列表预览 -->
                <div id="wordPreviewList" class="hidden">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-xs text-purple-600 font-medium">预览单词列表</span>
                        <button id="togglePreviewBtn" class="toggle-preview-btn text-xs text-purple-500 hover:text-purple-700">
                            展开查看
                        </button>
                    </div>
                    <div id="wordPreviewContent" class="word-preview-content hidden max-h-48 overflow-y-auto bg-white rounded-lg p-3 border border-purple-200">
                        <!-- 全选控制 -->
                        <div class="select-all-controls">
                            <div class="flex items-center justify-between">
                                <label class="flex items-center text-sm font-medium text-gray-700 cursor-pointer">
                                    <input type="checkbox" id="selectAllWords" class="word-checkbox mr-2">
                                    <span>全选单词</span>
                                </label>
                                <div class="flex items-center space-x-3">
                                    <span class="text-xs text-gray-600">
                                        已选择: <span id="selectedCount" class="selection-counter">0</span>
                                    </span>
                                    <div class="flex items-center space-x-2">
                                        <button id="selectDifficultBtn" class="text-xs text-orange-500 hover:text-orange-700 underline" title="选择困难单词">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>困难
                                        </button>
                                        <button id="clearSelectionBtn" class="clear-selection-btn text-xs text-red-500 hover:text-red-700">
                                            <i class="fas fa-times mr-1"></i>清空
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="wordPreviewItems" class="space-y-2"></div>
                    </div>
                </div>

                <div class="mt-2 text-xs text-purple-600 text-center">
                    实时预览筛选结果
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-center space-x-4">
                <button id="startSelectedReviewBtn" class="btn-primary text-white px-6 py-3 rounded-lg font-medium">
                    <i class="fas fa-play mr-2"></i>开始复习
                </button>
                <button id="cancelReviewSelectionBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
            </div>
        </div>

        <!-- 完成界面 -->
        <div id="completionScreen" class="bg-white rounded-xl card-shadow p-8 text-center hidden fade-in">
            <div class="mb-6">
                <i class="fas fa-trophy text-6xl text-yellow-500 mb-4"></i>
                <h2 class="text-3xl font-bold text-gray-800 mb-2">学习完成！</h2>
                <p class="text-gray-600">恭喜你完成了本次学习</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-green-50 p-6 rounded-lg">
                    <div id="finalCorrect" class="text-3xl font-bold text-green-600 mb-2">0</div>
                    <div class="text-gray-600">答对题数</div>
                </div>
                <div class="bg-red-50 p-6 rounded-lg">
                    <div id="finalWrong" class="text-3xl font-bold text-red-600 mb-2">0</div>
                    <div class="text-gray-600">答错题数</div>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg">
                    <div id="finalAccuracy" class="text-3xl font-bold text-blue-600 mb-2">0%</div>
                    <div class="text-gray-600">正确率</div>
                </div>
            </div>

            <div class="flex justify-center space-x-4">
                <button id="restartBtn" class="btn-primary text-white px-6 py-3 rounded-lg font-medium">
                    <i class="fas fa-redo mr-2"></i>再来一次
                </button>
                <button id="reviewErrorsBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-exclamation-triangle mr-2"></i>复习错题
                </button>
                <button id="backHomeBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-home mr-2"></i>返回主页
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script src="js/database.js"></script>
    <script src="js/ai-ocr.js"></script>
    <script src="js/llm-ocr.js"></script>
    <script src="js/ocr.js"></script>
    <script src="js/wordlist.js"></script>
    <script src="js/quiz.js"></script>
    <script src="js/app.js"></script>
</body>
</html>